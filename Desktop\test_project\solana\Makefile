# Pump.fun Token Monitor Makefile

.PHONY: help build run test clean docker fmt lint check install dev prod logs

# Default target
help: ## Show this help message
	@echo "Pump.fun Token Monitor - Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
build: ## Build the project in debug mode
	cargo build

build-release: ## Build the project in release mode
	cargo build --release

run: ## Run the project in development mode
	cargo run

run-release: ## Run the project in release mode (optimized)
	cargo run --release

dev: ## Run in development mode with debug logging
	RUST_LOG=debug cargo run

test: ## Run all tests
	cargo test

test-verbose: ## Run tests with verbose output
	cargo test -- --nocapture

integration-test: ## Run integration tests (requires running server)
	cargo test --test integration_test

# Code quality targets
fmt: ## Format code using rustfmt
	cargo fmt

lint: ## Run clippy linter
	cargo clippy -- -D warnings

check: ## Check code without building
	cargo check

audit: ## Run security audit
	cargo audit

# Docker targets
docker-build: ## Build Docker image
	docker build -t pump-fun-monitor .

docker-run: ## Run Docker container
	docker run -p 8080:8080 pump-fun-monitor

docker-compose-up: ## Start services with docker-compose
	docker-compose up -d

docker-compose-down: ## Stop services with docker-compose
	docker-compose down

docker-compose-logs: ## Show docker-compose logs
	docker-compose logs -f

# Production targets
prod: ## Run in production mode
	RUST_LOG=info cargo run --release -- --port 8080

install: ## Install binary to system
	cargo install --path .

# Utility targets
clean: ## Clean build artifacts
	cargo clean
	docker system prune -f

logs: ## Show application logs (if running via systemd)
	journalctl -u pump-fun-monitor -f

deps: ## Update dependencies
	cargo update

# Client examples
run-js-client: ## Run JavaScript test client
	node examples/test_client.js

run-py-client: ## Run Python test client
	python3 examples/test_client.py

# Setup targets
setup: ## Initial project setup
	@echo "Setting up pump.fun monitor..."
	@cp .env.example .env
	@echo "✅ Created .env file from template"
	@echo "✅ Run 'make build' to compile the project"
	@echo "✅ Run 'make run' to start the monitor"

setup-dev: ## Setup development environment
	@echo "Setting up development environment..."
	rustup component add rustfmt clippy
	cargo install cargo-audit
	@echo "✅ Development tools installed"

# Monitoring targets
health-check: ## Check if the service is healthy
	@curl -s http://localhost:8080/health || echo "Service not responding"

monitor-stats: ## Show connection statistics
	@echo "Checking WebSocket connections..."
	@netstat -an | grep :8080 || echo "No connections on port 8080"

# Benchmark targets
bench: ## Run benchmarks
	cargo bench

profile: ## Run with profiling
	RUST_LOG=info cargo run --release --features="profiling"

# Documentation targets
docs: ## Generate documentation
	cargo doc --open

docs-deploy: ## Generate and deploy documentation
	cargo doc --no-deps
	@echo "Documentation generated in target/doc/"

# Release targets
prepare-release: fmt lint test ## Prepare for release (format, lint, test)
	@echo "✅ Release preparation complete"

release-patch: ## Create patch release
	cargo release patch

release-minor: ## Create minor release
	cargo release minor

release-major: ## Create major release
	cargo release major

# Configuration
.env:
	cp .env.example .env

# Quick development workflow
quick-start: setup build run ## Quick start for new developers

# Full CI workflow
ci: fmt lint test build-release ## Run full CI pipeline locally

# Performance testing
load-test: ## Run load test with multiple connections
	@echo "Running load test..."
	@for i in {1..10}; do \
		(node examples/test_client.js &) \
	done
	@echo "Started 10 test clients"

# Service management (for production)
service-install: ## Install systemd service
	sudo cp pump-fun-monitor.service /etc/systemd/system/
	sudo systemctl daemon-reload
	sudo systemctl enable pump-fun-monitor

service-start: ## Start systemd service
	sudo systemctl start pump-fun-monitor

service-stop: ## Stop systemd service
	sudo systemctl stop pump-fun-monitor

service-status: ## Check systemd service status
	sudo systemctl status pump-fun-monitor

# Network and connectivity
check-solana: ## Check Solana RPC connectivity
	@echo "Testing Solana RPC connectivity..."
	@curl -s -X POST -H "Content-Type: application/json" \
		-d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' \
		https://api.mainnet-beta.solana.com | jq '.'

check-websocket: ## Test WebSocket connection
	@echo "Testing WebSocket connection..."
	@timeout 5 wscat -c ws://localhost:8080 || echo "WebSocket not available"

# Maintenance
backup-logs: ## Backup log files
	@mkdir -p backups
	@tar -czf backups/logs-$(shell date +%Y%m%d-%H%M%S).tar.gz logs/

rotate-logs: ## Rotate log files
	@echo "Rotating logs..."
	@find logs/ -name "*.log" -mtime +7 -delete

# Development utilities
watch: ## Watch for changes and rebuild
	cargo watch -x run

watch-test: ## Watch for changes and run tests
	cargo watch -x test

flamegraph: ## Generate flamegraph profile
	cargo flamegraph --bin pump-fun-monitor

# Version information
version: ## Show version information
	@echo "Rust version: $(shell rustc --version)"
	@echo "Cargo version: $(shell cargo --version)"
	@echo "Project version: $(shell cargo metadata --format-version 1 | jq -r '.packages[] | select(.name=="pump-fun-monitor") | .version')"