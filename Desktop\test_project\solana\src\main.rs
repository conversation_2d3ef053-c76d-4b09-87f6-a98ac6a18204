use anyhow::Result;
use clap::Parser;
use dotenv::dotenv;
use log::{error, info, warn};
use std::env;
use tokio::signal;

mod config;
mod error;
mod monitor;
mod server;
mod types;

use config::Config;
use monitor::SolanaMonitor;
use server::WebSocketServer;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    #[arg(short, long, default_value = "8080")]
    port: u16,

    #[arg(long, default_value = "wss://api.mainnet-beta.solana.com")]
    rpc_ws_url: String,

    #[arg(long, default_value = "https://api.mainnet-beta.solana.com")]
    rpc_http_url: String,

    #[arg(long, default_value = "info")]
    log_level: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    
    let args = Args::parse();
    
    env::set_var("RUST_LOG", &args.log_level);
    env_logger::init();

    info!("Starting Pump.fun Token Monitor");
    
    let config = Config {
        websocket_port: args.port,
        solana_rpc_ws_url: args.rpc_ws_url,
        solana_rpc_http_url: args.rpc_http_url,
        pump_fun_program_id: "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string(),
    };

    let (event_sender, event_receiver) = tokio::sync::broadcast::channel(1000);
    
    let server = WebSocketServer::new(config.websocket_port, event_receiver);
    let server_handle = tokio::spawn(async move {
        if let Err(e) = server.start().await {
            error!("WebSocket server error: {}", e);
        }
    });

    let monitor = SolanaMonitor::new(config, event_sender);
    let monitor_handle = tokio::spawn(async move {
        if let Err(e) = monitor.start().await {
            error!("Solana monitor error: {}", e);
        }
    });

    info!("Services started successfully");
    info!("WebSocket server listening on port {}", args.port);
    info!("Monitoring pump.fun contract for token creation events");

    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received shutdown signal");
        }
        _ = server_handle => {
            warn!("WebSocket server terminated");
        }
        _ = monitor_handle => {
            warn!("Solana monitor terminated");
        }
    }

    info!("Shutting down gracefully");
    Ok(())
}