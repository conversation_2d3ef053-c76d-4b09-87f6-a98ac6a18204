{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 3565836698897968655, "deps": [[40386456601120721, "percent_encoding", false, 7310510456210847196], [95042085696191081, "ipnet", false, 4277362228006234348], [264090853244900308, "sync_wrapper", false, 5797644894530554632], [784494742817713399, "tower_service", false, 9849383258613134295], [1044435446100926395, "hyper_rustls", false, 11567105715323873739], [1288403060204016458, "tokio_util", false, 16672959534115630532], [1906322745568073236, "pin_project_lite", false, 861604440337301281], [3150220818285335163, "url", false, 2954482302370443905], [3722963349756955755, "once_cell", false, 13060949280600448561], [4405182208873388884, "http", false, 15019771483751612326], [5986029879202738730, "log", false, 1381721069693530661], [7414427314941361239, "hyper", false, 4456425503393549868], [7620660491849607393, "futures_core", false, 8874305355213509553], [8405603588346937335, "winreg", false, 16969587642094373801], [8569119365930580996, "serde_json", false, 16622365724765313471], [8915503303801890683, "http_body", false, 8117171240454094168], [9689903380558560274, "serde", false, 184812589471625902], [10229185211513642314, "mime", false, 4431113436002033240], [10629569228670356391, "futures_util", false, 17917309277176397778], [11295624341523567602, "rustls", false, 10771137086337106490], [12186126227181294540, "tokio_native_tls", false, 12102601037613414596], [12367227501898450486, "hyper_tls", false, 14300421590494889261], [12944427623413450645, "tokio", false, 15843313876687712215], [13763625454224483636, "h2", false, 8782458187985646695], [14564311161534545801, "encoding_rs", false, 3366862287697480631], [14721851354164625169, "async_compression", false, 10996381552921367737], [16066129441945555748, "bytes", false, 9175216007531460610], [16311359161338405624, "rustls_pemfile", false, 7014611222957825063], [16542808166767769916, "serde_urlencoded", false, 564497501225861351], [16622232390123975175, "tokio_rustls", false, 15455204230368356808], [16785601910559813697, "native_tls_crate", false, 16616665314469593075], [17652733826348741533, "webpki_roots", false, 10929826152452987510], [18066890886671768183, "base64", false, 260666117457335573]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-307d9b47f69c70f8\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}