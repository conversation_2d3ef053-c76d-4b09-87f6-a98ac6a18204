# Pump.fun Token Monitor

A high-performance Rust-based WebSocket service that monitors the Solana blockchain for pump.fun token creation events and streams them to connected clients in real-time.

## Features

- **Real-time Monitoring**: Connects to Solana RPC WebSocket to monitor pump.fun contract events
- **WebSocket Server**: Broadcasts token creation events to multiple connected clients
- **Robust Error Handling**: Comprehensive error handling with automatic reconnection
- **High Performance**: Asynchronous architecture using Tokio for efficient concurrent operations
- **Clean JSON API**: Well-structured JSON messages for easy client integration
- **Production Ready**: Proper logging, graceful shutdown, and resource management

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Solana RPC    │    │  Token Monitor   │    │  WebSocket      │
│   WebSocket     ├────┤  Service         ├────┤  Clients        │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Rust 1.70+ 
- Access to Solana RPC endpoints (mainnet or devnet)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pump-fun-monitor
```

2. Install dependencies:
```bash
cargo build --release
```

3. Create a `.env` file (optional):
```env
RUST_LOG=info
SOLANA_RPC_WS_URL=wss://api.mainnet-beta.solana.com
SOLANA_RPC_HTTP_URL=https://api.mainnet-beta.solana.com
WEBSOCKET_PORT=8080
```

### Running the Service

#### Basic Usage
```bash
cargo run
```

#### With Custom Configuration
```bash
cargo run -- --port 9090 --rpc-ws-url wss://api.devnet.solana.com --log-level debug
```

#### Command Line Options
- `--port, -p`: WebSocket server port (default: 8080)
- `--rpc-ws-url`: Solana RPC WebSocket URL 
- `--rpc-http-url`: Solana RPC HTTP URL
- `--log-level`: Log level (trace, debug, info, warn, error)

## API Documentation

### WebSocket Connection

Connect to the WebSocket server:
```javascript
const ws = new WebSocket('ws://localhost:8080');
```

### Message Types

#### Connection Confirmation
```json
{
  "type": "connected",
  "client_id": "uuid-v4-string"
}
```

#### Token Creation Event
```json
{
  "type": "token_created",
  "event_type": "token_created",
  "timestamp": "2024-01-15T10:30:45Z",
  "transaction_signature": "5x7K8m9N2q3R4s5T6u7V8w9X0y1Z2a3B4c5D6e7F8g9H0i1J2k3L4m5N6o7P8q9R0s",
  "token": {
    "mint_address": "ABC123def456GHI789jkl012MNO345pqr678STU901vwx234YZ",
    "name": "MyAwesomeToken",
    "symbol": "MAT",
    "creator": "DEF456ghi789JKL012mno345PQR678stu901VWX234yz567AB",
    "supply": 1000000000,
    "decimals": 6
  },
  "pump_data": {
    "bonding_curve": "GHI789jkl012MNO345pqr678STU901vwx234YZ567abc890DE",
    "virtual_sol_reserves": 30000000000,
    "virtual_token_reserves": 1073000000000000
  }
}
```

#### Error Message
```json
{
  "type": "error",
  "message": "Error description"
}
```

#### Ping/Pong
```json
{
  "type": "ping"
}
```

```json
{
  "type": "pong"
}
```

### Client Example

#### JavaScript/Node.js
```javascript
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8080');

ws.on('open', () => {
  console.log('Connected to pump.fun monitor');
});

ws.on('message', (data) => {
  const message = JSON.parse(data);
  
  if (message.type === 'token_created') {
    console.log('New token created:', message.token.name);
    console.log('Symbol:', message.token.symbol);
    console.log('Creator:', message.token.creator);
    console.log('Transaction:', message.transaction_signature);
  }
});

ws.on('error', (error) => {
  console.error('WebSocket error:', error);
});
```

#### Python
```python
import asyncio
import websockets
import json

async def monitor_tokens():
    uri = "ws://localhost:8080"
    
    async with websockets.connect(uri) as websocket:
        print("Connected to pump.fun monitor")
        
        async for message in websocket:
            data = json.loads(message)
            
            if data.get('type') == 'token_created':
                token = data['token']
                print(f"New token: {token['name']} ({token['symbol']})")
                print(f"Creator: {token['creator']}")
                print(f"Transaction: {data['transaction_signature']}")

asyncio.run(monitor_tokens())
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `RUST_LOG` | Log level | `info` |
| `SOLANA_RPC_WS_URL` | Solana WebSocket RPC URL | `wss://api.mainnet-beta.solana.com` |
| `SOLANA_RPC_HTTP_URL` | Solana HTTP RPC URL | `https://api.mainnet-beta.solana.com` |
| `WEBSOCKET_PORT` | WebSocket server port | `8080` |

### RPC Endpoints

#### Mainnet
- WebSocket: `wss://api.mainnet-beta.solana.com`
- HTTP: `https://api.mainnet-beta.solana.com`

#### Devnet  
- WebSocket: `wss://api.devnet.solana.com`
- HTTP: `https://api.devnet.solana.com`

#### Custom RPC Providers
You can use custom RPC providers like:
- Alchemy: `wss://solana-mainnet.g.alchemy.com/v2/your-api-key`
- QuickNode: `wss://your-endpoint.solana.quiknode.pro/your-api-key`
- Helius: `wss://rpc.helius.xyz`

## Development

### Project Structure
```
src/
├── main.rs          # Application entry point
├── config.rs        # Configuration management
├── monitor.rs       # Solana blockchain monitor
├── server.rs        # WebSocket server
├── types.rs         # Data types and structures
└── error.rs         # Error handling
```

### Running Tests
```bash
cargo test
```

### Running with Debug Logging
```bash
RUST_LOG=debug cargo run
```

### Code Formatting
```bash
cargo fmt
```

### Linting
```bash
cargo clippy
```

## Production Deployment

### Docker Support

Create a `Dockerfile`:
```dockerfile
FROM rust:1.70 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/pump-fun-monitor /usr/local/bin/

EXPOSE 8080
CMD ["pump-fun-monitor"]
```

Build and run:
```bash
docker build -t pump-fun-monitor .
docker run -p 8080:8080 pump-fun-monitor
```

### Systemd Service

Create `/etc/systemd/system/pump-fun-monitor.service`:
```ini
[Unit]
Description=Pump.fun Token Monitor
After=network.target

[Service]
Type=simple
User=pump-monitor
WorkingDirectory=/opt/pump-fun-monitor
ExecStart=/opt/pump-fun-monitor/pump-fun-monitor
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Performance Tuning

For high-throughput scenarios:
- Increase WebSocket message buffer size
- Use connection pooling for RPC requests  
- Consider running multiple instances behind a load balancer
- Monitor memory usage and implement backpressure if needed

## Monitoring & Observability

### Metrics

The service logs important metrics:
- Connected client count
- Event processing rate
- Connection errors and reconnections
- Message broadcast statistics

### Health Checks

You can monitor service health by:
- Checking WebSocket connection status
- Monitoring log output for errors
- Implementing custom health check endpoints

### Troubleshooting

#### Common Issues

1. **Connection Failures**: Check RPC endpoint availability and network connectivity
2. **High Memory Usage**: Monitor client connections and implement limits if needed
3. **Missing Events**: Verify pump.fun program ID and subscription parameters
4. **Slow Performance**: Check RPC response times and consider using premium endpoints

#### Debug Mode

Run with detailed logging:
```bash
RUST_LOG=debug cargo run
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run `cargo test` and `cargo clippy`
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is provided as-is for educational and development purposes. Always test thoroughly before using in production environments. The authors are not responsible for any financial losses or damages that may occur from using this software.