{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 7325118914443436841, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-c3bcf8b1f5d3148d\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}