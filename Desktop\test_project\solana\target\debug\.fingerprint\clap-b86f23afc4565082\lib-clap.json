{"rustc": 16591470773350601817, "features": "[\"atty\", \"cargo\", \"color\", \"default\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 15657897354478470176, "path": 5579843859795024487, "deps": [[580378868546634928, "textwrap", false, 9868555806845744794], [3722963349756955755, "once_cell", false, 13060949280600448561], [5841926810058920975, "strsim", false, 4106842434885023223], [10058577953979766589, "atty", false, 10046753143518606001], [10435729446543529114, "bitflags", false, 16050946518276884541], [12902659978838094914, "termcolor", false, 9284406873212682162], [14923790796823607459, "indexmap", false, 1823237546956777711], [15944592714770878610, "clap_lex", false, 10141748448046616453]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-b86f23afc4565082\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}