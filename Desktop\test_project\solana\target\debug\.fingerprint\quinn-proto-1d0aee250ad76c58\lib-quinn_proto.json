{"rustc": 16591470773350601817, "features": "[\"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "declared_features": "[\"arbitrary\", \"default\", \"log\", \"native-certs\", \"ring\", \"rustls\", \"rustls-native-certs\", \"tls-rustls\"]", "target": 15092862010867938684, "profile": 15657897354478470176, "path": 3895197026276959388, "deps": [[1042707345065476716, "tinyvec", false, 11304794413198718078], [3016319839805820069, "ring", false, 2642489083508204127], [5451793922601807560, "slab", false, 8028805928982091419], [8008191657135824715, "thiserror", false, 17648284592145713875], [8606274917505247608, "tracing", false, 2409203978462943042], [11295624341523567602, "rustls", false, 10771137086337106490], [13208667028893622512, "rand", false, 13985128861025382709], [14394652928131349565, "rustls_native_certs", false, 1650697506318594299], [16055916053474393816, "rustc_hash", false, 11920667106460871862], [16066129441945555748, "bytes", false, 9175216007531460610]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\quinn-proto-1d0aee250ad76c58\\dep-lib-quinn_proto", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}