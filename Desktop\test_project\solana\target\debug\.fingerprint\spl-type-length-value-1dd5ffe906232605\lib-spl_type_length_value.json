{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"derive\"]", "target": 1877903327968078873, "profile": 15657897354478470176, "path": 10919548956355476539, "deps": [[58942224022519477, "solana_program", false, 2803416228122252333], [6511429716036861196, "bytemuck", false, 6182604353071293094], [11699822774991256268, "spl_pod", false, 15132546471853725712], [14673743079976092479, "spl_program_error", false, 5515069432211551144], [18269786033916185670, "spl_discriminator", false, 2126650267895387983]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\spl-type-length-value-1dd5ffe906232605\\dep-lib-spl_type_length_value", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}