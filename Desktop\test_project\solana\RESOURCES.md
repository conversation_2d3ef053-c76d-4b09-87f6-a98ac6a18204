# Resources and Documentation Links

## 📚 Solana RPC and WebSocket Resources

### Official Solana Documentation
- **WebSocket API**: https://docs.solana.com/api/websocket
- **RPC API Reference**: https://docs.solana.com/api/http
- **RPC Endpoint Information**: https://solana.com/rpc
- **Commitment Levels**: https://docs.solana.com/api/http#configuring-state-commitment

### Rust Solana SDK
- **Main Solana Repository**: https://github.com/solana-labs/solana
- **Solana Client Crate**: https://docs.rs/solana-client/
- **Core Solana SDK**: https://docs.rs/solana-sdk/
- **Account Decoder**: https://docs.rs/solana-account-decoder/

## 🎯 Pump.fun Specific Resources

### Contract Information
- **Program ID**: `6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P`
- **Solscan Explorer**: https://solscan.io/account/6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
- **Solana Explorer**: https://explorer.solana.com/address/6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P

### Pump.fun Documentation
- **Official Website**: https://pump.fun
- **API Documentation**: https://docs.pump.fun (if available)
- **Program Instructions**: Available through on-chain analysis

## 🌐 Rust WebSocket Libraries

### WebSocket Crates
- **Tokio Tungstenite**: https://docs.rs/tokio-tungstenite/
  - Async WebSocket implementation built on Tokio
  - Best choice for high-performance async applications
- **Tungstenite**: https://docs.rs/tungstenite/
  - Synchronous WebSocket library
  - Lower-level, more control over connection handling
- **WebSocket Crate**: https://docs.rs/websocket/
  - Alternative WebSocket implementation

### Async Runtime
- **Tokio**: https://docs.rs/tokio/
  - Primary async runtime for Rust
- **Tokio Tutorial**: https://tokio.rs/tokio/tutorial
  - Comprehensive guide to async programming in Rust

## 🔧 JSON-RPC and Serialization

### RPC Libraries
- **JSON-RPC Core**: https://docs.rs/jsonrpc-core/
  - JSON-RPC 2.0 implementation
- **Reqwest**: https://docs.rs/reqwest/
  - HTTP client for making RPC calls
- **Serde**: https://docs.rs/serde/
  - Serialization framework
- **Serde JSON**: https://docs.rs/serde_json/
  - JSON serialization support

## 🔍 Solana Program Interaction

### Account Parsing
- **Borsh**: https://docs.rs/borsh/
  - Binary serialization format used by Solana
- **Anchor Lang**: https://docs.rs/anchor-lang/
  - Framework for Solana program development
- **Solana Program**: https://docs.rs/solana-program/
  - On-chain program utilities

## 🛠️ Development Tools

### Testing and Monitoring
- **Solana Explorer**: https://explorer.solana.com/
  - Official blockchain explorer
- **Solscan**: https://solscan.io/
  - Alternative block explorer with better UX
- **Env Logger**: https://docs.rs/env_logger/
  - Logging implementation for debugging

### Additional Tools
- **Base58**: https://docs.rs/bs58/
  - Base58 encoding/decoding for Solana addresses
- **UUID**: https://docs.rs/uuid/
  - UUID generation for client identification
- **Chrono**: https://docs.rs/chrono/
  - Date and time handling

## 🌍 Sample RPC Endpoints for Testing

### Mainnet Endpoints
```
WebSocket: wss://api.mainnet-beta.solana.com
HTTP: https://api.mainnet-beta.solana.com
```

### Devnet Endpoints (Recommended for Testing)
```
WebSocket: wss://api.devnet.solana.com
HTTP: https://api.devnet.solana.com
```

### Testnet Endpoints
```
WebSocket: wss://api.testnet.solana.com
HTTP: https://api.testnet.solana.com
```

### Premium RPC Providers

#### Alchemy
```
WebSocket: wss://solana-mainnet.g.alchemy.com/v2/YOUR_API_KEY
HTTP: https://solana-mainnet.g.alchemy.com/v2/YOUR_API_KEY
```

#### QuickNode
```
WebSocket: wss://your-endpoint.solana.quiknode.pro/YOUR_API_KEY
HTTP: https://your-endpoint.solana.quiknode.pro/YOUR_API_KEY
```

#### Helius
```
WebSocket: wss://rpc.helius.xyz
HTTP: https://rpc.helius.xyz
```

#### GenesysGo
```
WebSocket: wss://ssc-dao.genesysgo.net
HTTP: https://ssc-dao.genesysgo.net
```

## 📊 Expected Data Format Examples

### RPC Subscription Request
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "programSubscribe",
  "params": [
    "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
    {
      "encoding": "base64",
      "commitment": "confirmed"
    }
  ]
}
```

### RPC Subscription Response
```json
{
  "jsonrpc": "2.0",
  "method": "programNotification",
  "params": {
    "result": {
      "context": {
        "slot": *********
      },
      "value": {
        "account": {
          "data": "base64encodeddata==",
          "executable": false,
          "lamports": 2039280,
          "owner": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
          "rentEpoch": 361
        },
        "pubkey": "TokenMintAddress123..."
      }
    },
    "subscription": 123
  }
}
```

### Transaction Details Response
```json
{
  "jsonrpc": "2.0",
  "result": {
    "blockTime": **********,
    "meta": {
      "err": null,
      "fee": 5000,
      "innerInstructions": [],
      "logMessages": [
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1]",
        "Program log: Instruction: Create",
        "Program log: InitializeMint",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 45123 of 200000 compute units",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P success"
      ],
      "postBalances": [**********, 2039280, 1461600],
      "preBalances": [**********, 0, 1461600],
      "rewards": [],
      "status": {"Ok": null}
    },
    "slot": *********,
    "transaction": {
      "message": {
        "accountKeys": [
          "CreatorAddress123...",
          "TokenMintAddress123...",
          "11111111111111111111111111111112"
        ],
        "header": {
          "numReadonlySignedAccounts": 0,
          "numReadonlyUnsignedAccounts": 1,
          "numRequiredSignatures": 1
        },
        "instructions": [
          {
            "accounts": [0, 1, 2],
            "data": "base64instructiondata==",
            "programIdIndex": 3
          }
        ],
        "recentBlockhash": "BlockhashString123..."
      },
      "signatures": ["TransactionSignature123..."]
    }
  },
  "id": 1
}
```

### Our Application Output
```json
{
  "event_type": "token_created",
  "timestamp": "2024-01-15T10:30:45.123Z",
  "transaction_signature": "5x7K8m9N2q3R4s5T6u7V8w9X0y1Z2a3B4c5D6e7F8g9H0i1J2k3L4m5N6o7P8q9R0s",
  "token": {
    "mint_address": "ABC123def456GHI789jkl012MNO345pqr678STU901vwx234YZ",
    "name": "MyAwesomeToken",
    "symbol": "MAT",
    "creator": "DEF456ghi789JKL012mno345PQR678stu901VWX234yz567AB",
    "supply": **********,
    "decimals": 6
  },
  "pump_data": {
    "bonding_curve": "GHI789jkl012MNO345pqr678STU901vwx234YZ567abc890DE",
    "virtual_sol_reserves": ***********,
    "virtual_token_reserves": ****************
  }
}
```

## 🔗 Additional Resources

### Community Resources
- **Solana Discord**: https://discord.gg/solana
- **Solana Stack Exchange**: https://solana.stackexchange.com/
- **Rust Discord**: https://discord.gg/rust-lang

### Learning Materials
- **Solana Cookbook**: https://solanacookbook.com/
- **Anchor Book**: https://book.anchor-lang.com/
- **Rust Book**: https://doc.rust-lang.org/book/

### Performance and Monitoring
- **Solana Beach**: https://solanabeach.io/
- **Solana Status**: https://status.solana.com/
- **RPC Performance**: Monitor response times and choose optimal endpoints

## ⚠️ Important Notes

### Rate Limits
- Free RPC endpoints have rate limits (typically 100 requests/minute)
- Consider premium providers for production use
- Implement exponential backoff for failed requests

### WebSocket Connection Limits
- Most RPC providers limit concurrent WebSocket connections
- Implement connection pooling for high-throughput applications
- Monitor connection health and implement reconnection logic

### Data Consistency
- Use appropriate commitment levels for your use case
- `confirmed` is recommended for most applications
- `finalized` provides highest security but higher latency

### Security Considerations
- Never expose RPC API keys in client-side code
- Use environment variables for sensitive configuration
- Implement proper error handling to avoid information leakage