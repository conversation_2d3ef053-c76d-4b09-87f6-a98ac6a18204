{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 5953538683061555654, "deps": [[5103565458935487, "futures_io", false, 8482567523261729805], [1811549171721445101, "futures_channel", false, 17739180735480847453], [7013762810557009322, "futures_sink", false, 14198883556002926449], [7620660491849607393, "futures_core", false, 8874305355213509553], [10629569228670356391, "futures_util", false, 17917309277176397778], [12779779637805422465, "futures_executor", false, 1788316423954660225], [16240732885093539806, "futures_task", false, 12713175797586825385]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-b65150f59a9cadad\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}