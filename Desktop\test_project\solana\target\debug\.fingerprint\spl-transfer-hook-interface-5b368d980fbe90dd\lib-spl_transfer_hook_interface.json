{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 15657897354478470176, "path": 7992945810186490534, "deps": [[*****************, "solana_program", false, 2803416228122252333], [6511429716036861196, "bytemuck", false, 6182604353071293094], [7479230097745373958, "spl_type_length_value", false, 15470948475519695310], [9529943735784919782, "arrayref", false, 5004488932003906412], [11699822774991256268, "spl_pod", false, 15132546471853725712], [12414676574469740085, "spl_tlv_account_resolution", false, 1750650915083667235], [14673743079976092479, "spl_program_error", false, 5515069432211551144], [18269786033916185670, "spl_discriminator", false, 2126650267895387983]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\spl-transfer-hook-interface-5b368d980fbe90dd\\dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}