{"rustc": 16591470773350601817, "features": "[\"no-entrypoint\"]", "declared_features": "[\"no-entrypoint\", \"test-sbf\"]", "target": 13653270454399553497, "profile": 15657897354478470176, "path": 17703591756325174680, "deps": [[58942224022519477, "solana_program", false, 2803416228122252333], [2611905835808443941, "borsh", false, 11603590664185568048], [4827030885814306469, "spl_token", false, 5361122957892792058], [5157631553186200874, "num_traits", false, 490087604393875952], [7445376950542623896, "spl_token_2022", false, 876578402769790370], [8008191657135824715, "thiserror", false, 17648284592145713875], [11263754829263059703, "num_derive", false, 9337745526955844253], [16912878040847476921, "assert_matches", false, 13511798959420164653]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\spl-associated-token-account-5e64fa7f395f55ef\\dep-lib-spl_associated_token_account", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}